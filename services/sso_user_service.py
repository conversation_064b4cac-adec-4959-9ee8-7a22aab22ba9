"""
* @class SsoUserService
* @description This class is responsible for handling user-related operations with the SSO backend server.
* <AUTHOR>
"""

import os
from typing import List, Dict, Optional
import requests
import pathlib
from utils.logger import get_debug_logger

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))
logger = get_debug_logger(
    "sso_user_service", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)


class SsoUserService:
    def __init__(self):
        self.sso_internal_server = os.getenv("SSO_INTERNAL_SERVER")

        if not self.sso_internal_server:
            logger.warning("SSO_INTERNAL_SERVER environment variable is not set")

    def get_user_list(
        self, team_id: str, user_types: List[int], user_id_list: Optional[List[str]] = None
    ) -> List[Dict]:
        """
        Get user list from SSO backend server

        Args:
            team_id (str): ID of the team
            user_types (List[int]): List of user types to filter
            user_id_list (List[str], optional): List of specific user IDs to fetch

        Returns:
            List[Dict]: List of user details containing id, name, email and userType

        Raises:
            requests.exceptions.RequestException: If API call fails
        """
        try:
            if not self.sso_internal_server:
                logger.error("SSO_INTERNAL_SERVER or GET_ALL_USERS_URL environment variables are not set")
                return []

            get_users_url = self.sso_internal_server + "/internal/getUserList"

            details = {"teamId": team_id, "userTypes": user_types, "userIdList": user_id_list or []}

            response = requests.post(url=get_users_url, json=details)

            response.raise_for_status()  # Raises exception for 4XX/5XX status codes

            user_list = response.json()

            if not user_list:
                logger.error(
                    f"Get user list | SsoUserService.get_user_list | {team_id} | No data returned from SSO server"
                )
                return []

            return user_list

        except requests.exceptions.RequestException as err:
            logger.error(
                f"Get user list | SsoUserService.get_user_list | {team_id} | "
                f"API call failed with error: {str(err)}"
            )
            return []

    def login(self, credentials: Dict) -> Dict:
        """
        Authenticate user through SSO backend server

        Args:
            credentials (Dict): Dictionary containing email and password

        Returns:
            Dict: Login response from SSO server containing token and user info

        Raises:
            requests.exceptions.RequestException: If API call fails
        """
        try:
            if not self.sso_internal_server:
                logger.error("SSO_INTERNAL_SERVER environment variable is not set")
                raise requests.exceptions.RequestException("SSO server configuration missing")

            login_url = self.sso_internal_server + "/users/login"

            response = requests.post(url=login_url, json=credentials)

            response.raise_for_status()  # Raises exception for 4XX/5XX status codes

            login_result = response.json()

            logger.info(f"Login successful for user: {credentials.get('email', 'unknown')}")
            return login_result

        except requests.exceptions.RequestException as err:
            logger.error(
                f"Login failed | SsoUserService.login | {credentials.get('email', 'unknown')} | "
                f"API call failed with error: {str(err)}"
            )
            raise err
